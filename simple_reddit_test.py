#!/usr/bin/env python3
"""
Simple Reddit test - works without credentials using public Reddit API
This script tests basic Reddit connectivity and post retrieval
Extended to send posts to Discord via localhost:5000
"""
import requests
import json
from datetime import datetime
from typing import List, Dict, Any
from config import Config
import time

def test_public_reddit_api() -> bool:
    """Test Reddit's public API without authentication"""
    print("🔗 Testing Public Reddit API...")
    
    try:
        # Test basic Reddit connectivity
        url = "https://www.reddit.com/r/test.json"
        headers = {'User-Agent': 'RedditTestBot/1.0'}
        
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        if 'data' in data and 'children' in data['data']:
            print("✅ Reddit API is accessible")
            return True
        else:
            print("❌ Unexpected Reddit API response format")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Reddit API connection failed: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON response from Reddit: {e}")
        return False

def fetch_posts_from_subreddit(subreddit: str, limit: int = 10) -> List[Dict[str, Any]]:
    """Fetch posts from a specific subreddit using public API"""
    print(f"📰 Fetching posts from r/{subreddit}...")
    
    try:
        url = f"https://www.reddit.com/r/{subreddit}/new.json?limit={limit}"
        headers = {'User-Agent': 'RedditTestBot/1.0'}
        
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        data = response.json()
        posts = []
        
        if 'data' in data and 'children' in data['data']:
            for child in data['data']['children']:
                post_data = child['data']
                
                # Extract relevant post information
                post = {
                    'id': post_data.get('id', ''),
                    'title': post_data.get('title', ''),
                    'subreddit': post_data.get('subreddit', ''),
                    'author': post_data.get('author', '[deleted]'),
                    'score': post_data.get('score', 0),
                    'num_comments': post_data.get('num_comments', 0),
                    'created_utc': post_data.get('created_utc', 0),
                    'permalink': post_data.get('permalink', ''),
                    'url': f"https://reddit.com{post_data.get('permalink', '')}",
                    'selftext': post_data.get('selftext', '')[:200]
                }
                posts.append(post)
            
            print(f"   ✅ Successfully fetched {len(posts)} posts from r/{subreddit}")
            return posts
        else:
            print(f"   ❌ No posts found in r/{subreddit}")
            return []
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Error fetching from r/{subreddit}: {e}")
        return []
    except json.JSONDecodeError as e:
        print(f"   ❌ Invalid JSON from r/{subreddit}: {e}")
        return []

def load_queries_from_file(filename: str = 'queries.txt') -> list:
    """Load and parse queries from the queries.txt file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                print("❌ queries.txt is empty")
                return []
            queries = []
            for line in content.split('\n'):
                if line.strip():
                    line_queries = [q.strip().strip('"\'').lower() for q in line.split(' OR ') if q.strip()]
                    queries.extend(line_queries)
            print(f"   📝 Loaded {len(queries)} search terms from {filename}")
            return queries
    except Exception as e:
        print(f"❌ Error loading queries: {e}")
        return []

def post_matches_queries(post: Dict[str, Any], queries: list) -> bool:
    title_lower = post['title'].lower()
    content_lower = post['selftext'].lower()
    for query in queries:
        if query in title_lower or query in content_lower:
            return True
    return False

def send_post_to_discord(post: Dict[str, Any]) -> bool:
    """Send a Reddit post to Discord using the webhook URL from config"""
    webhook_url = getattr(Config, 'DISCORD_WEBHOOK_URL', None)
    if not webhook_url:
        print("❌ Discord webhook URL not configured in Config.DISCORD_WEBHOOK_URL")
        return False
    print(f"📤 Sending post to Discord via webhook: {webhook_url[:60]}...")

    try:
        # Format the main content for Discord
        created_time = datetime.fromtimestamp(post['created_utc']).strftime('%Y-%m-%d %H:%M:%S')
        summary = f"**{post['title'][:256]}**\n"
        summary += f"[View on Reddit]({post['url']})\n"
        summary += f"Subreddit: r/{post['subreddit']} | Author: u/{post['author']}\n"
        summary += f"👍 {post['score']} | 💬 {post['num_comments']} | 🕒 {created_time}"
        if post['selftext']:
            preview = post['selftext'][:200]
            if len(post['selftext']) > 200:
                preview += "..."
            summary += f"\n\n{preview}"

        # Prepare the payload for Discord webhook
        discord_payload = {
            "content": summary,
            "embeds": [
                {
                    "title": post['title'][:256],
                    "url": post['url'],
                    "color": 0x5865f2,
                    "timestamp": datetime.fromtimestamp(post['created_utc']).isoformat() + "Z",
                    "footer": {"text": f"r/{post['subreddit']}"},
                    "author": {
                        "name": f"u/{post['author']}",
                        "url": f"https://reddit.com/u/{post['author']}"
                    },
                    "fields": [
                        {
                            "name": "📊 Stats",
                            "value": f"👍 {post['score']} • 💬 {post['num_comments']}",
                            "inline": True
                        },
                        {
                            "name": "🕒 Posted",
                            "value": created_time,
                            "inline": True
                        }
                    ]
                }
            ]
        }
        # Add content field if post has text
        if post['selftext']:
            content_preview = post['selftext'][:500]
            if len(post['selftext']) > 500:
                content_preview += "..."
            discord_payload["embeds"][0]["fields"].append({
                "name": "📝 Content",
                "value": content_preview,
                "inline": False
            })

        headers = {'Content-Type': 'application/json'}
        response = requests.post(webhook_url, json=discord_payload, headers=headers, timeout=10)
        response.raise_for_status()

        print(f"   ✅ Successfully sent post to Discord: {post['title'][:50]}...")
        return True

    except requests.exceptions.RequestException as e:
        print(f"   ❌ Failed to send to Discord: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error sending to Discord: {e}")
        return False

def reddit_to_discord_bot():
    """Main bot loop: fetch posts, match queries, send to Discord"""
    print("🤖 Starting Reddit-to-Discord bot...")
    subreddits = ['forhire', 'slavelabour', 'Jobs4Bitcoins', 'programming']
    queries = load_queries_from_file('queries.txt')
    if not queries:
        print("❌ No queries loaded. Exiting.")
        return
    sent_post_ids = set()
    fetch_limit = 10
    poll_interval = 60  # seconds
    print(f"🔄 Monitoring subreddits: {', '.join(subreddits)}")
    print(f"🔁 Polling every {poll_interval} seconds...")
    while True:
        for subreddit in subreddits:
            posts = fetch_posts_from_subreddit(subreddit, fetch_limit)
            for post in posts:
                if post['id'] in sent_post_ids:
                    continue
                if post_matches_queries(post, queries):
                    print(f"🎯 Match found in r/{subreddit}: {post['title'][:60]}...")
                    success = send_post_to_discord(post)
                    if success:
                        sent_post_ids.add(post['id'])
        print(f"⏳ Sleeping for {poll_interval} seconds...")
        time.sleep(poll_interval)

if __name__ == "__main__":
    try:
        reddit_to_discord_bot()
    except KeyboardInterrupt:
        print("\n\n⏹️  Bot interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
