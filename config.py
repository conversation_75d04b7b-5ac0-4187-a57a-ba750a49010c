"""
Configuration management for Reddit Discord Bot
"""
import os
from dotenv import load_dotenv
from typing import List, Optional

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for Reddit Discord Bot"""
    
    # Reddit API Configuration
    REDDIT_CLIENT_ID: str = os.getenv('REDDIT_CLIENT_ID', '')
    REDDIT_CLIENT_SECRET: str = os.getenv('REDDIT_CLIENT_SECRET', '')
    REDDIT_USER_AGENT: str = os.getenv('REDDIT_USER_AGENT', 'RedditDiscordBot/1.0')
    
    # Discord Configuration
    DISCORD_WEBHOOK_URL: str = os.getenv('DISCORD_WEBHOOK_URL', '')
    DISCORD_BOT_TOKEN: str = os.getenv('DISCORD_BOT_TOKEN', '')
    DISCORD_CHANNEL_ID: str = os.getenv('DISCORD_CHANNEL_ID', '')
    
    # Monitoring Configuration
    SUBREDDITS: List[str] = os.getenv('SUBREDDITS', 'forhire,slavelabour,Jobs4Bitcoins').split(',')
    CHECK_INTERVAL: int = int(os.getenv('CHECK_INTERVAL', '30'))  # seconds
    QUERIES_FILE: str = os.getenv('QUERIES_FILE', 'queries.txt')
    
    # Logging Configuration
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE: str = os.getenv('LOG_FILE', 'reddit_bot.log')
    
    # Rate Limiting
    MAX_POSTS_PER_CHECK: int = int(os.getenv('MAX_POSTS_PER_CHECK', '25'))
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """Validate configuration and return list of missing required settings"""
        errors = []
        
        if not cls.REDDIT_CLIENT_ID:
            errors.append("REDDIT_CLIENT_ID is required")
        if not cls.REDDIT_CLIENT_SECRET:
            errors.append("REDDIT_CLIENT_SECRET is required")
        
        # Either webhook URL or bot token + channel ID required
        if not cls.DISCORD_WEBHOOK_URL and not (cls.DISCORD_BOT_TOKEN and cls.DISCORD_CHANNEL_ID):
            errors.append("Either DISCORD_WEBHOOK_URL or (DISCORD_BOT_TOKEN + DISCORD_CHANNEL_ID) is required")
        
        if not cls.SUBREDDITS or cls.SUBREDDITS == ['']:
            errors.append("SUBREDDITS is required")
        
        return errors
    
    @classmethod
    def get_subreddits_string(cls) -> str:
        """Get subreddits as a '+' separated string for PRAW"""
        return '+'.join(cls.SUBREDDITS)

    @classmethod
    def get_test_credentials(cls) -> dict:
        """Get credentials for testing (read-only access)"""
        return {
            'client_id': cls.REDDIT_CLIENT_ID or 'test_client_id',
            'client_secret': cls.REDDIT_CLIENT_SECRET or 'test_client_secret',
            'user_agent': cls.REDDIT_USER_AGENT
        }
