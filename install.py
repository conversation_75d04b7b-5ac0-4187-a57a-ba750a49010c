#!/usr/bin/env python3
"""
Installation and setup script for Reddit Discord Bot
"""
import os
import sys
import subprocess
import shutil

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"❌ Python 3.7+ required, found {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def install_dependencies():
    """Install Python dependencies"""
    if not os.path.exists('requirements.txt'):
        print("❌ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python dependencies"
    )

def setup_config_files():
    """Set up configuration files"""
    success = True
    
    # Copy .env.example to .env if it doesn't exist
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            try:
                shutil.copy('.env.example', '.env')
                print("✅ Created .env file from template")
            except Exception as e:
                print(f"❌ Failed to create .env file: {e}")
                success = False
        else:
            print("❌ .env.example not found")
            success = False
    else:
        print("ℹ️  .env file already exists")
    
    # Check if queries.txt exists
    if not os.path.exists('queries.txt'):
        print("⚠️  queries.txt not found - you'll need to create this file")
        success = False
    else:
        print("✅ queries.txt found")
    
    return success

def main():
    """Main installation function"""
    print("🚀 Reddit Discord Bot Installation")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Failed to install dependencies")
        sys.exit(1)
    
    # Set up config files
    config_success = setup_config_files()
    
    print("\n" + "=" * 50)
    print("📋 Installation Summary")
    print("=" * 50)
    
    if config_success:
        print("✅ Installation completed successfully!")
        print("\n📝 Next steps:")
        print("1. Edit .env file with your Reddit and Discord credentials")
        print("2. Edit queries.txt with your search queries")
        print("3. Run: python run.py")
        print("\n📖 See README.md for detailed setup instructions")
    else:
        print("⚠️  Installation completed with warnings")
        print("\n📝 Required actions:")
        print("1. Create and configure .env file (see .env.example)")
        print("2. Create queries.txt with your search queries")
        print("3. Edit .env file with your Reddit and Discord credentials")
        print("4. Run: python run.py")
        print("\n📖 See README.md for detailed setup instructions")

if __name__ == "__main__":
    main()
