# Reddit Discord Bot

A Python automation script that monitors Reddit for new posts in real-time and sends Discord notifications when posts match specified criteria.

## Features

- **Real-time Reddit monitoring**: Continuously fetches new posts from specified subreddits
- **Flexible query matching**: Supports complex search queries with OR operators
- **Discord notifications**: Sends rich embed notifications via webhook or bot
- **Robust error handling**: Handles API rate limits, network issues, and authentication failures
- **Comprehensive logging**: Tracks monitoring activity, matches, and errors
- **Circuit breaker pattern**: Prevents cascading failures during API outages
- **Configurable**: Easy to customize for different subreddits and queries

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Reddit API Setup

1. Go to https://www.reddit.com/prefs/apps
2. Click "Create App" or "Create Another App"
3. Choose "script" as the app type
4. Note down your `client_id` and `client_secret`

### 3. Discord Setup

Choose one of the following methods:

#### Method A: Discord Webhook (Recommended - Easier)
1. Go to your Discord server settings
2. Navigate to Integrations → Webhooks
3. Create a new webhook for your desired channel
4. Copy the webhook URL

#### Method B: Discord Bot
1. Go to https://discord.com/developers/applications
2. Create a new application and bot
3. Copy the bot token
4. Invite the bot to your server with "Send Messages" permission
5. Get your channel ID (enable Developer Mode in Discord, right-click channel)

### 4. Configuration

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your credentials:
   ```env
   # Reddit API
   REDDIT_CLIENT_ID=your_reddit_client_id_here
   REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
   
   # Discord (choose one method)
   DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook_url_here
   # OR
   # DISCORD_BOT_TOKEN=your_discord_bot_token_here
   # DISCORD_CHANNEL_ID=your_discord_channel_id_here
   
   # Monitoring settings
   SUBREDDITS=forhire,slavelabour,Jobs4Bitcoins
   CHECK_INTERVAL=30
   ```

3. Edit `queries.txt` with your search terms:
   ```
   "hiring developer" OR "freelance developer" OR "developer needed"
   "python script" OR "automation"
   ```

### 5. Run the Bot

```bash
python main.py
```

## Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `REDDIT_CLIENT_ID` | Reddit API client ID | Required |
| `REDDIT_CLIENT_SECRET` | Reddit API client secret | Required |
| `REDDIT_USER_AGENT` | User agent for Reddit API | `RedditDiscordBot/1.0` |
| `DISCORD_WEBHOOK_URL` | Discord webhook URL | Optional |
| `DISCORD_BOT_TOKEN` | Discord bot token | Optional |
| `DISCORD_CHANNEL_ID` | Discord channel ID | Optional |
| `SUBREDDITS` | Comma-separated list of subreddits | `forhire,slavelabour,Jobs4Bitcoins` |
| `CHECK_INTERVAL` | Seconds between checks | `30` |
| `QUERIES_FILE` | Path to queries file | `queries.txt` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `LOG_FILE` | Log file path | `reddit_bot.log` |
| `MAX_POSTS_PER_CHECK` | Maximum posts to check per cycle | `25` |

### Query Format

The `queries.txt` file supports:
- One query per line
- OR operators for multiple terms: `"term1" OR "term2" OR "term3"`
- Case-insensitive matching
- Quotes are optional but recommended for phrases

Example:
```
"hiring developer" OR "freelance developer" OR "developer needed"
"python script" OR "automation developer"
"discord bot" OR "telegram bot"
```

## Monitoring

The bot provides comprehensive logging and statistics:

- **Console output**: Real-time status and important events
- **Log file**: Detailed logs with rotation (10MB max, 5 backups)
- **Discord status**: Startup/shutdown notifications with statistics
- **Periodic stats**: Hourly statistics logged automatically

## Error Handling

The bot includes robust error handling:

- **Rate limiting**: Automatic backoff and retry for Reddit/Discord APIs
- **Circuit breaker**: Prevents cascading failures during API outages
- **Network issues**: Automatic retry with exponential backoff
- **Authentication**: Clear error messages for invalid credentials
- **Graceful shutdown**: Handles SIGINT/SIGTERM signals properly

## Troubleshooting

### Common Issues

1. **"Reddit API access forbidden"**
   - Check your Reddit API credentials
   - Ensure your app type is set to "script"

2. **"Discord authentication failed"**
   - Verify your webhook URL or bot token
   - Check bot permissions if using bot method

3. **"No queries loaded"**
   - Check that `queries.txt` exists and has content
   - Verify the file path in configuration

4. **Rate limiting**
   - The bot handles this automatically
   - Consider increasing `CHECK_INTERVAL` if frequent

### Logs

Check the log file (`reddit_bot.log` by default) for detailed error information:

```bash
tail -f reddit_bot.log
```

## License

This project is open source and available under the MIT License.
