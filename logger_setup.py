"""
Logging setup for Reddit Discord Bot
"""
import logging
import logging.handlers
import os
from datetime import datetime
from config import Config

def setup_logging() -> logging.Logger:
    """
    Set up logging configuration for the Reddit Discord Bot
    
    Returns:
        Configured logger instance
    """
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(Config.LOG_FILE) if os.path.dirname(Config.LOG_FILE) else '.'
    if log_dir != '.' and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO))
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        Config.LOG_FILE,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Create application logger
    app_logger = logging.getLogger('reddit_discord_bot')
    
    return app_logger

def log_startup_info(logger: logging.Logger) -> None:
    """Log startup information"""
    logger.info("=" * 50)
    logger.info("Reddit Discord Bot Starting Up")
    logger.info("=" * 50)
    logger.info(f"Log Level: {Config.LOG_LEVEL}")
    logger.info(f"Log File: {Config.LOG_FILE}")
    logger.info(f"Subreddits: {', '.join(Config.SUBREDDITS)}")
    logger.info(f"Check Interval: {Config.CHECK_INTERVAL} seconds")
    logger.info(f"Max Posts Per Check: {Config.MAX_POSTS_PER_CHECK}")
    logger.info(f"Queries File: {Config.QUERIES_FILE}")
    
    # Log Discord configuration (without sensitive data)
    if Config.DISCORD_WEBHOOK_URL:
        logger.info("Discord: Using webhook")
    elif Config.DISCORD_BOT_TOKEN and Config.DISCORD_CHANNEL_ID:
        logger.info(f"Discord: Using bot token (Channel ID: {Config.DISCORD_CHANNEL_ID})")
    else:
        logger.warning("Discord: No valid configuration found")
    
    logger.info("=" * 50)

def log_statistics(logger: logging.Logger, stats: dict) -> None:
    """Log periodic statistics"""
    logger.info("--- Statistics ---")
    for key, value in stats.items():
        logger.info(f"{key}: {value}")
    logger.info("--- End Statistics ---")

class BotStats:
    """Track and log bot statistics"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.posts_checked = 0
        self.matches_found = 0
        self.notifications_sent = 0
        self.errors_encountered = 0
        self.last_match_time = None
        self.last_error_time = None
    
    def increment_posts_checked(self, count: int = 1) -> None:
        """Increment posts checked counter"""
        self.posts_checked += count
    
    def increment_matches_found(self) -> None:
        """Increment matches found counter"""
        self.matches_found += 1
        self.last_match_time = datetime.now()
    
    def increment_notifications_sent(self) -> None:
        """Increment notifications sent counter"""
        self.notifications_sent += 1
    
    def increment_errors(self) -> None:
        """Increment errors counter"""
        self.errors_encountered += 1
        self.last_error_time = datetime.now()
    
    def get_stats_dict(self) -> dict:
        """Get statistics as dictionary"""
        uptime = datetime.now() - self.start_time
        
        return {
            "Uptime": str(uptime).split('.')[0],  # Remove microseconds
            "Posts Checked": self.posts_checked,
            "Matches Found": self.matches_found,
            "Notifications Sent": self.notifications_sent,
            "Errors Encountered": self.errors_encountered,
            "Last Match": self.last_match_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_match_time else 'Never',
            "Last Error": self.last_error_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_error_time else 'Never'
        }
    
    def log_periodic_stats(self, logger: logging.Logger) -> None:
        """Log periodic statistics"""
        log_statistics(logger, self.get_stats_dict())
