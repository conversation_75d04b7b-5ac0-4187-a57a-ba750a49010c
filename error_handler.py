"""
Error handling utilities for Reddit Discord Bot
"""
import time
import logging
import requests
from typing import Callable, Any, Optional
from functools import wraps
from prawcore.exceptions import ResponseException, RequestException, Forbidden, NotFound
from requests.exceptions import RequestException as ReqException, Timeout, ConnectionError

logger = logging.getLogger(__name__)

class BotError(Exception):
    """Base exception for bot-specific errors"""
    pass

class ConfigurationError(BotError):
    """Raised when configuration is invalid"""
    pass

class APIError(BotError):
    """Raised when API calls fail"""
    pass

class RateLimitError(APIError):
    """Raised when rate limit is exceeded"""
    pass

def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator to retry function calls on failure with exponential backoff
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        backoff: Multiplier for delay after each retry
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries: {e}")
                        break
                    
                    # Check if this is a retryable error
                    if not _is_retryable_error(e):
                        logger.error(f"Non-retryable error in {func.__name__}: {e}")
                        break
                    
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. Retrying in {current_delay}s...")
                    time.sleep(current_delay)
                    current_delay *= backoff
            
            raise last_exception
        
        return wrapper
    return decorator

def _is_retryable_error(error: Exception) -> bool:
    """Check if an error is retryable"""
    retryable_errors = (
        ConnectionError,
        Timeout,
        ReqException,
        RequestException,
        ResponseException
    )
    
    # Don't retry on authentication or permission errors
    non_retryable_errors = (
        Forbidden,
        NotFound,
        ConfigurationError
    )
    
    if isinstance(error, non_retryable_errors):
        return False
    
    if isinstance(error, retryable_errors):
        return True
    
    # Check for specific HTTP status codes
    if hasattr(error, 'response') and error.response:
        status_code = error.response.status_code
        # Don't retry on client errors (4xx) except rate limiting
        if 400 <= status_code < 500 and status_code != 429:
            return False
    
    return True

def handle_reddit_errors(func: Callable) -> Callable:
    """Decorator to handle Reddit API specific errors"""
    @wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        try:
            return func(*args, **kwargs)
        except Forbidden as e:
            logger.error(f"Reddit API access forbidden: {e}")
            raise APIError(f"Reddit API access forbidden: {e}")
        except NotFound as e:
            logger.error(f"Reddit resource not found: {e}")
            raise APIError(f"Reddit resource not found: {e}")
        except ResponseException as e:
            if hasattr(e, 'response') and e.response.status_code == 429:
                logger.warning("Reddit API rate limit exceeded")
                raise RateLimitError("Reddit API rate limit exceeded")
            logger.error(f"Reddit API error: {e}")
            raise APIError(f"Reddit API error: {e}")
        except RequestException as e:
            logger.error(f"Reddit request error: {e}")
            raise APIError(f"Reddit request error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in Reddit operation: {e}")
            raise APIError(f"Unexpected Reddit error: {e}")
    
    return wrapper

def handle_discord_errors(func: Callable) -> Callable:
    """Decorator to handle Discord API specific errors"""
    @wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        try:
            return func(*args, **kwargs)
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:
                # Discord rate limit
                retry_after = e.response.headers.get('Retry-After', '1')
                logger.warning(f"Discord rate limit exceeded. Retry after {retry_after}s")
                raise RateLimitError(f"Discord rate limit exceeded. Retry after {retry_after}s")
            elif e.response.status_code == 401:
                logger.error("Discord authentication failed")
                raise APIError("Discord authentication failed")
            elif e.response.status_code == 403:
                logger.error("Discord access forbidden")
                raise APIError("Discord access forbidden")
            elif e.response.status_code == 404:
                logger.error("Discord resource not found")
                raise APIError("Discord resource not found")
            else:
                logger.error(f"Discord HTTP error: {e}")
                raise APIError(f"Discord HTTP error: {e}")
        except requests.exceptions.RequestException as e:
            logger.error(f"Discord request error: {e}")
            raise APIError(f"Discord request error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in Discord operation: {e}")
            raise APIError(f"Unexpected Discord error: {e}")
    
    return wrapper

def safe_execute(func: Callable, *args, **kwargs) -> tuple[bool, Any]:
    """
    Safely execute a function and return success status and result
    
    Returns:
        Tuple of (success: bool, result: Any)
    """
    try:
        result = func(*args, **kwargs)
        return True, result
    except Exception as e:
        logger.error(f"Error executing {func.__name__}: {e}")
        return False, None

def handle_rate_limit(error: Exception, service: str = "API") -> None:
    """Handle rate limit errors with appropriate delays"""
    if isinstance(error, RateLimitError):
        # Extract retry delay if available
        delay = 60  # Default delay
        
        if "Retry after" in str(error):
            try:
                delay = int(str(error).split("Retry after ")[1].split("s")[0])
            except (ValueError, IndexError):
                pass
        
        logger.warning(f"{service} rate limit hit. Waiting {delay} seconds...")
        time.sleep(delay)
    else:
        # General rate limit handling
        logger.warning(f"Possible rate limit for {service}. Waiting 30 seconds...")
        time.sleep(30)

class ErrorRecovery:
    """Handles error recovery and circuit breaker pattern"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 300):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.circuit_open = False
    
    def record_success(self) -> None:
        """Record a successful operation"""
        self.failure_count = 0
        self.circuit_open = False
    
    def record_failure(self) -> None:
        """Record a failed operation"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.circuit_open = True
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")
    
    def can_execute(self) -> bool:
        """Check if operation can be executed"""
        if not self.circuit_open:
            return True
        
        # Check if recovery timeout has passed
        if time.time() - self.last_failure_time > self.recovery_timeout:
            logger.info("Circuit breaker recovery timeout reached, attempting to close circuit")
            self.circuit_open = False
            self.failure_count = 0
            return True
        
        return False
    
    def get_status(self) -> dict:
        """Get current circuit breaker status"""
        return {
            'circuit_open': self.circuit_open,
            'failure_count': self.failure_count,
            'last_failure_time': self.last_failure_time,
            'time_until_recovery': max(0, self.recovery_timeout - (time.time() - self.last_failure_time))
        }
