#!/usr/bin/env python3
"""
Reddit to Discord Integration Script
Fetches a Reddit post and sends it to Discord via localhost:5000
"""
import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional

def fetch_reddit_post(subreddit: str = "programming") -> Optional[Dict[str, Any]]:
    """
    Fetch a single Reddit post from the specified subreddit
    
    Args:
        subreddit: The subreddit to fetch from (default: programming)
        
    Returns:
        Dictionary containing post data or None if failed
    """
    print(f"📡 Fetching post from r/{subreddit}...")
    
    try:
        url = f"https://www.reddit.com/r/{subreddit}/hot.json?limit=1"
        headers = {'User-Agent': 'RedditToDiscordBot/1.0'}
        
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        if 'data' in data and 'children' in data['data'] and data['data']['children']:
            post_data = data['data']['children'][0]['data']
            
            # Extract relevant post information
            post = {
                'id': post_data.get('id', ''),
                'title': post_data.get('title', ''),
                'subreddit': post_data.get('subreddit', ''),
                'author': post_data.get('author', '[deleted]'),
                'score': post_data.get('score', 0),
                'num_comments': post_data.get('num_comments', 0),
                'created_utc': post_data.get('created_utc', 0),
                'permalink': post_data.get('permalink', ''),
                'url': f"https://reddit.com{post_data.get('permalink', '')}",
                'selftext': post_data.get('selftext', '')
            }
            
            print(f"   ✅ Successfully fetched post: {post['title'][:50]}...")
            return post
        else:
            print(f"   ❌ No posts found in r/{subreddit}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Error fetching from r/{subreddit}: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"   ❌ Invalid JSON from r/{subreddit}: {e}")
        return None

def create_discord_embed(post: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a Discord embed from Reddit post data
    
    Args:
        post: Dictionary containing Reddit post data
        
    Returns:
        Dictionary containing Discord embed data
    """
    created_time = datetime.fromtimestamp(post['created_utc']).strftime('%Y-%m-%d %H:%M:%S')
    
    embed = {
        "title": post['title'][:256],  # Discord title limit
        "url": post['url'],
        "color": 0xFF4500,  # Reddit orange
        "timestamp": datetime.fromtimestamp(post['created_utc']).isoformat() + "Z",
        "footer": {
            "text": f"r/{post['subreddit']} • Reddit",
            "icon_url": "https://www.redditstatic.com/desktop2x/img/favicon/android-icon-192x192.png"
        },
        "author": {
            "name": f"u/{post['author']}",
            "url": f"https://reddit.com/u/{post['author']}"
        },
        "fields": [
            {
                "name": "📊 Stats",
                "value": f"👍 {post['score']} upvotes • 💬 {post['num_comments']} comments",
                "inline": True
            },
            {
                "name": "🕒 Posted",
                "value": created_time,
                "inline": True
            }
        ]
    }
    
    # Add content field if post has text
    if post['selftext'] and post['selftext'].strip():
        content_preview = post['selftext'][:500]
        if len(post['selftext']) > 500:
            content_preview += "..."
        embed["fields"].append({
            "name": "📝 Content",
            "value": content_preview,
            "inline": False
        })
    
    return embed

def send_to_discord(embed: Dict[str, Any], webhook_url: str = "http://localhost:5000") -> bool:
    """
    Send embed to Discord via webhook URL
    
    Args:
        embed: Discord embed dictionary
        webhook_url: URL to send the webhook to (default: localhost:5000)
        
    Returns:
        True if successful, False otherwise
    """
    print(f"📤 Sending to Discord via {webhook_url}...")
    
    try:
        payload = {
            "username": "Reddit Bot",
            "avatar_url": "https://www.redditstatic.com/desktop2x/img/favicon/android-icon-192x192.png",
            "embeds": [embed]
        }
        
        headers = {'Content-Type': 'application/json'}
        response = requests.post(webhook_url, json=payload, headers=headers, timeout=10)
        response.raise_for_status()
        
        print(f"   ✅ Successfully sent to Discord!")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Failed to send to Discord: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def main():
    """Main function to fetch Reddit post and send to Discord"""
    print("🤖 Reddit to Discord Bot")
    print("=" * 30)
    
    # Get subreddit from user or use default
    try:
        subreddit = input("Enter subreddit name (default: programming): ").strip()
        if not subreddit:
            subreddit = "programming"
    except KeyboardInterrupt:
        print("\n\n⏹️  Interrupted by user")
        return
    
    # Fetch Reddit post
    post = fetch_reddit_post(subreddit)
    if not post:
        print("\n❌ Failed to fetch Reddit post")
        return
    
    # Display post info
    print(f"\n📋 Post Details:")
    print(f"   Title: {post['title']}")
    print(f"   Author: u/{post['author']}")
    print(f"   Subreddit: r/{post['subreddit']}")
    print(f"   Score: {post['score']} upvotes")
    print(f"   Comments: {post['num_comments']}")
    print(f"   URL: {post['url']}")
    
    # Create Discord embed
    embed = create_discord_embed(post)
    
    # Send to Discord
    success = send_to_discord(embed)
    
    if success:
        print("\n🎉 Successfully sent Reddit post to Discord!")
        print("\n💡 Check your Discord channel to see the message")
    else:
        print("\n❌ Failed to send to Discord")
        print("💡 Make sure localhost:5000 is running and accepting POST requests")
        print("💡 You can test with a Discord webhook URL instead")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Bot interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
