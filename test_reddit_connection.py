#!/usr/bin/env python3
"""
Test script to verify Reddit connection and post retrieval
This script tests if the bot can successfully connect to Reddit and fetch posts
"""
import sys
import os
from datetime import datetime
from typing import List, Dict, Any

# Add current directory to path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import praw
    from config import Config
    from query_parser import QueryParser
    from logger_setup import setup_logging
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you've installed dependencies: pip install -r requirements.txt")
    sys.exit(1)

class RedditTester:
    """Test Reddit connectivity and post retrieval"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.reddit = None
        self.query_parser = None
    
    def test_configuration(self) -> bool:
        """Test if configuration is valid"""
        print("🔧 Testing Configuration...")
        
        # Check if .env file exists
        if not os.path.exists('.env'):
            print("❌ .env file not found")
            return False
        
        # Validate config
        errors = Config.validate_config()
        if errors:
            print("❌ Configuration errors found:")
            for error in errors:
                print(f"   - {error}")
            print("\n💡 To fix Reddit API errors:")
            print("   1. Go to https://www.reddit.com/prefs/apps")
            print("   2. Create a new 'script' application")
            print("   3. Add your client_id and client_secret to .env file")
            return False
        
        print("✅ Configuration is valid")
        return True
    
    def test_reddit_connection(self) -> bool:
        """Test Reddit API connection"""
        print("\n🔗 Testing Reddit Connection...")
        
        try:
            # Initialize Reddit instance
            self.reddit = praw.Reddit(
                client_id=Config.REDDIT_CLIENT_ID,
                client_secret=Config.REDDIT_CLIENT_SECRET,
                user_agent=Config.REDDIT_USER_AGENT
            )
            
            # Test connection by accessing Reddit info
            print(f"   User Agent: {self.reddit.config.user_agent}")
            print(f"   Read Only: {self.reddit.read_only}")
            
            # Try to access a simple endpoint
            self.reddit.user.me()
            print("✅ Reddit API connection successful")
            return True
            
        except Exception as e:
            print(f"❌ Reddit connection failed: {e}")
            print("\n💡 Common fixes:")
            print("   - Check your REDDIT_CLIENT_ID and REDDIT_CLIENT_SECRET in .env")
            print("   - Make sure your Reddit app type is 'script'")
            print("   - Verify your credentials at https://www.reddit.com/prefs/apps")
            return False
    
    def test_subreddit_access(self) -> bool:
        """Test access to configured subreddits"""
        print("\n📋 Testing Subreddit Access...")
        
        if not self.reddit:
            print("❌ Reddit not connected")
            return False
        
        success = True
        for subreddit_name in Config.SUBREDDITS:
            try:
                subreddit = self.reddit.subreddit(subreddit_name)
                # Try to access subreddit info
                subscribers = subreddit.subscribers
                print(f"   ✅ r/{subreddit_name}: {subscribers:,} subscribers")
            except Exception as e:
                print(f"   ❌ r/{subreddit_name}: {e}")
                success = False
        
        return success
    
    def test_query_parser(self) -> bool:
        """Test query parsing functionality"""
        print("\n🔍 Testing Query Parser...")
        
        try:
            self.query_parser = QueryParser()
            query_count = self.query_parser.get_query_count()
            
            if query_count == 0:
                print("❌ No queries loaded from queries.txt")
                return False
            
            print(f"   ✅ Loaded {query_count} search queries")
            
            # Show first few queries
            queries = self.query_parser.get_queries()
            print("   📝 Sample queries:")
            for i, query in enumerate(queries[:5]):
                print(f"      {i+1}. {query}")
            if len(queries) > 5:
                print(f"      ... and {len(queries) - 5} more")
            
            # Test query matching
            test_title = "Looking for a Python developer for freelance work"
            is_match, matched_query = self.query_parser.check_match(test_title)
            if is_match:
                print(f"   ✅ Query matching works (matched: '{matched_query}')")
            else:
                print("   ⚠️  Query matching test didn't find a match")
            
            return True
            
        except Exception as e:
            print(f"❌ Query parser error: {e}")
            return False
    
    def fetch_sample_posts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Fetch sample posts from configured subreddits"""
        print(f"\n📰 Fetching {limit} Recent Posts...")
        
        if not self.reddit:
            print("❌ Reddit not connected")
            return []
        
        posts = []
        try:
            # Get combined subreddit
            subreddits_string = Config.get_subreddits_string()
            subreddit = self.reddit.subreddit(subreddits_string)
            
            print(f"   📡 Fetching from: r/{subreddits_string}")
            
            # Fetch new posts
            for post in subreddit.new(limit=limit):
                post_data = {
                    'id': post.id,
                    'title': post.title,
                    'subreddit': post.subreddit.display_name,
                    'author': str(post.author) if post.author else '[deleted]',
                    'score': post.score,
                    'num_comments': post.num_comments,
                    'created_utc': post.created_utc,
                    'url': f"https://reddit.com{post.permalink}",
                    'content': post.selftext[:200] + "..." if len(post.selftext) > 200 else post.selftext
                }
                posts.append(post_data)
            
            print(f"   ✅ Successfully fetched {len(posts)} posts")
            return posts
            
        except Exception as e:
            print(f"   ❌ Error fetching posts: {e}")
            return []
    
    def test_post_matching(self, posts: List[Dict[str, Any]]) -> None:
        """Test if any fetched posts match our queries"""
        print("\n🎯 Testing Post Matching...")
        
        if not self.query_parser or not posts:
            print("❌ No query parser or posts available")
            return
        
        matches_found = 0
        
        for post in posts:
            is_match, matched_query = self.query_parser.check_post_match(
                post['title'], 
                post['content']
            )
            
            if is_match:
                matches_found += 1
                print(f"   ✅ MATCH FOUND!")
                print(f"      Title: {post['title'][:60]}...")
                print(f"      Subreddit: r/{post['subreddit']}")
                print(f"      Matched Query: '{matched_query}'")
                print(f"      URL: {post['url']}")
                print()
        
        if matches_found == 0:
            print("   ℹ️  No matches found in current posts")
            print("   💡 This is normal - matches depend on current Reddit activity")
        else:
            print(f"   🎉 Found {matches_found} matching posts!")
    
    def display_sample_posts(self, posts: List[Dict[str, Any]]) -> None:
        """Display sample posts for verification"""
        print("\n📋 Sample Posts Retrieved:")
        print("=" * 80)
        
        for i, post in enumerate(posts[:5], 1):
            created_time = datetime.fromtimestamp(post['created_utc']).strftime('%Y-%m-%d %H:%M:%S')
            print(f"{i}. [{post['subreddit']}] {post['title'][:50]}...")
            print(f"   Author: u/{post['author']} | Score: {post['score']} | Comments: {post['num_comments']}")
            print(f"   Created: {created_time}")
            print(f"   URL: {post['url']}")
            if post['content']:
                print(f"   Content: {post['content'][:100]}...")
            print()
        
        if len(posts) > 5:
            print(f"... and {len(posts) - 5} more posts")
    
    def run_full_test(self) -> bool:
        """Run complete test suite"""
        print("🧪 Reddit Discord Bot - Connection Test")
        print("=" * 50)
        
        # Test configuration
        if not self.test_configuration():
            return False
        
        # Test Reddit connection
        if not self.test_reddit_connection():
            return False
        
        # Test subreddit access
        if not self.test_subreddit_access():
            return False
        
        # Test query parser
        if not self.test_query_parser():
            return False
        
        # Fetch sample posts
        posts = self.fetch_sample_posts(15)
        if not posts:
            return False
        
        # Display sample posts
        self.display_sample_posts(posts)
        
        # Test post matching
        self.test_post_matching(posts)
        
        print("\n" + "=" * 50)
        print("🎉 All tests completed successfully!")
        print("✅ Your bot should work correctly with these settings")
        print("\n💡 Next steps:")
        print("   1. Add your Discord webhook URL to .env")
        print("   2. Run: python run.py")
        
        return True

def main():
    """Main test function"""
    tester = RedditTester()
    
    try:
        success = tester.run_full_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during testing: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
