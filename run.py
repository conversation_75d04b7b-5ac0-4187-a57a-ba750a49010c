#!/usr/bin/env python3
"""
Simple startup script for Reddit Discord Bot
"""
import sys
import os

def check_requirements():
    """Check if all requirements are met"""
    errors = []
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        errors.append("❌ .env file not found. Copy .env.example to .env and configure it.")
    
    # Check if queries.txt exists
    if not os.path.exists('queries.txt'):
        errors.append("❌ queries.txt file not found. Create it with your search queries.")
    
    # Try importing required modules
    try:
        import praw
    except ImportError:
        errors.append("❌ praw not installed. Run: pip install -r requirements.txt")
    
    try:
        import requests
    except ImportError:
        errors.append("❌ requests not installed. Run: pip install -r requirements.txt")
    
    try:
        import dotenv
    except ImportError:
        errors.append("❌ python-dotenv not installed. Run: pip install -r requirements.txt")
    
    return errors

def main():
    """Main startup function"""
    print("🤖 Reddit Discord Bot Startup")
    print("=" * 40)
    
    # Check requirements
    errors = check_requirements()
    
    if errors:
        print("Setup issues found:")
        for error in errors:
            print(f"  {error}")
        print("\nPlease fix these issues and try again.")
        print("See README.md for detailed setup instructions.")
        sys.exit(1)
    
    print("✅ All requirements met!")
    print("🚀 Starting Reddit Discord Bot...")
    print("=" * 40)
    
    # Import and run the main bot
    try:
        from main import main as bot_main
        bot_main()
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        print(f"\n❌ Bot crashed: {e}")
        print("Check the log file for more details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
